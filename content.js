// 内容脚本，将在匹配的页面上运行
console.log('单品宝填充价格扩展已加载');

// 确保消息监听器在页面加载时就注册
console.log('注册消息监听器');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  console.log('content.js收到消息:', request);
  
  if (request.action === 'fillPrices') {
    try {
      const result = fillSkuPrices(request.skuPriceMap);
      console.log('填充结果:', result);
      sendResponse(result);
    } catch (error) {
      console.error('填充价格时出错:', error);
      sendResponse({ success: false, message: error.message });
    }
  } else {
    console.log('未知操作:', request.action);
    sendResponse({ success: false, message: '未知操作' });
  }
  
  // 返回true表示将异步发送响应
  return true;
});

// 填充SKU价格
function fillSkuPrices(skuPriceMap) {
  console.log('开始填充SKU价格');
  
  // 检查是否存在SKU促销价设置弹窗
  const dialog = document.querySelector('.sku-setting-dialog');
  if (!dialog) {
    console.error('未找到SKU促销价设置弹窗');
    return { success: false, message: '未找到SKU促销价设置弹窗' };
  }

  // 获取所有SKU行
  const rows = dialog.querySelectorAll('.next-table-body .next-table-row');
  if (!rows || rows.length === 0) {
    console.error('未找到SKU行');
    return { success: false, message: '未找到SKU行' };
  }

  console.log(`找到 ${rows.length} 行SKU`);
  let filledCount = 0;
  
  // 按照键的长度对SKU价格映射进行排序，确保更具体的SKU名称优先匹配
  const sortedSkuEntries = Object.entries(skuPriceMap).sort((a, b) => b[0].length - a[0].length);
  console.log('排序后的SKU条目:', sortedSkuEntries);

  // 遍历每一行
  rows.forEach((row, index) => {
    // 获取SKU名称
    const skuNameCell = row.querySelector('[data-next-table-col="1"] .next-table-cell-wrapper');
    if (!skuNameCell) {
      console.log(`行 ${index}: 未找到SKU名称单元格`);
      return;
    }
    
    const fullSkuName = skuNameCell.textContent.trim();
    console.log(`行 ${index}: SKU名称 = "${fullSkuName}"`);
    
    // 查找匹配的SKU价格（使用排序后的条目）
    for (const [key, price] of sortedSkuEntries) {
      if (fullSkuName.indexOf(key) === 0) {
        // 找到价格输入框
        const priceInput = row.querySelector('[data-next-table-col="3"] input');
        if (priceInput) {
          // 设置价格
          priceInput.value = price;
          // 触发input事件，确保价格更新
          const event = new Event('input', { bubbles: true });
          priceInput.dispatchEvent(event);
          
          // 同时触发change事件，确保价格被系统接受
          const changeEvent = new Event('change', { bubbles: true });
          priceInput.dispatchEvent(changeEvent);
          
          console.log(`行 ${index}: 已填充价格 ${price} (匹配 "${key}")`);
          filledCount++;
        } else {
          console.log(`行 ${index}: 未找到价格输入框`);
        }
        break;
      }
    }
  });

  // 如果有填充价格，显示提示
  if (filledCount > 0) {
    showCompletionMessage(filledCount);
  }

  return { success: true, count: filledCount };
}

// 显示填写完成提示
function showCompletionMessage(count) {
  // 创建提示元素
  const message = document.createElement('div');
  message.textContent = `填写完成！共填充 ${count} 个SKU价格`;
  message.style.position = 'fixed';
  message.style.top = '50%';
  message.style.left = '50%';
  message.style.transform = 'translate(-50%, -50%)';
  message.style.padding = '10px 20px';
  message.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  message.style.color = 'white';
  message.style.borderRadius = '4px';
  message.style.fontSize = '16px';
  message.style.zIndex = '9999';
  
  // 添加到页面
  document.body.appendChild(message);
  
  // 3秒后移除
  setTimeout(() => {
    document.body.removeChild(message);
  }, 3000);
}

// 添加一个测试函数，可以从控制台直接调用
window.testFillPrices = function(skuPriceMapStr) {
  try {
    let skuPriceMap;
    
    if (typeof skuPriceMapStr === 'string') {
      skuPriceMap = JSON.parse(skuPriceMapStr);
    } else {
      skuPriceMap = skuPriceMapStr;
    }
    
    const result = fillSkuPrices(skuPriceMap);
    console.log('测试结果:', result);
    return result;
  } catch (error) {
    console.error('测试出错:', error);
    return { success: false, message: error.message };
  }
};

// 确保函数在全局作用域可用
document.addEventListener('DOMContentLoaded', function() {
  window.testFillPrices = window.testFillPrices || function() {
    console.error('testFillPrices 函数未正确加载');
  };
});

// 页面加载完成后执行
console.log('单品宝填充价格扩展初始化完成'); 