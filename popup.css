body {
  width: 350px;
  font-family: 'Microsoft YaHei', sans-serif;
}

.container {
  padding: 10px;
  text-align: center;
}

h1 {
  font-size: 18px;
  color: #ff4400;
}

p {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Microsoft YaHei', sans-serif;
  margin-bottom: 10px;
  resize: vertical;
}

.button-container {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

button {
  background-color: #ff4400;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 14px;
}

button:hover {
  background-color: #1a73e8;
}

#status {
  margin-top: 10px;
  font-size: 14px;
  color: #333;
}

.log-container {
  height: 150px;
  overflow-y: auto;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  margin-top: 10px;
  font-family: monospace;
  font-size: 12px;
}

#logContent {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

#importXLS {
  background-color: #34a853;
}

#executeTask {
  background-color: #fbbc05;
}

#importXLS:hover {
  background-color: #2d9147;
}

#executeTask:hover {
  background-color: #f2b600;
} 