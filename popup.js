// 弹出窗口的基础脚本
document.addEventListener('DOMContentLoaded', function() {
  const fillPricesButton = document.getElementById('fillPrices');
  const skuPriceDataTextarea = document.getElementById('skuPriceData');
  const statusDiv = document.getElementById('status');
  const importXLSButton = document.getElementById('importXLS');
  const executeTaskButton = document.getElementById('executeTask');
  const logContent = document.getElementById('logContent');
  
  // 用于存储解析后的商品数据
  let parsedProducts = [];
  
  // 添加粘贴事件监听器，优化粘贴内容
  skuPriceDataTextarea.addEventListener('paste', function(e) {
    // 阻止默认粘贴行为
    e.preventDefault();
    
    // 获取剪贴板数据
    const clipboardData = e.clipboardData || window.clipboardData;
    let pastedText = clipboardData.getData('text');
    
    // 优化粘贴的内容
    pastedText = optimizePastedText(pastedText);
    
    // 将优化后的内容插入到文本框
    // 获取当前选中的文本范围
    const startPos = this.selectionStart;
    const endPos = this.selectionEnd;
    
    // 替换选中的文本或在光标位置插入
    const beforeText = this.value.substring(0, startPos);
    const afterText = this.value.substring(endPos);
    this.value = beforeText + pastedText + afterText;
    
    // 设置新的光标位置
    this.selectionStart = this.selectionEnd = startPos + pastedText.length;
    
    // 显示优化提示
    statusDiv.textContent = '已优化粘贴内容格式';
    setTimeout(() => {
      if (statusDiv.textContent === '已优化粘贴内容格式') {
        statusDiv.textContent = '';
      }
    }, 2000);
  });
  
  // 优化粘贴的文本
  function optimizePastedText(text) {
    // 1. 按行分割
    let lines = text.split(/\r?\n/);
    
    // 2. 处理每一行，去除空行
    lines = lines.map(line => line.trim()).filter(line => line);
    
    // 3. 处理每一行的内容
    lines = lines.map(line => {
      // 替换制表符和多个连续空格为单个空格
      line = line.replace(/\t+/g, ' ').replace(/\s+/g, ' ').trim();
      
      // 查找最后一个空格前的所有内容作为SKU名称
      const lastSpaceIndex = line.lastIndexOf(' ');
      if (lastSpaceIndex === -1) return line;
      
      const skuName = line.substring(0, lastSpaceIndex).trim();
      const price = line.substring(lastSpaceIndex + 1).trim();
      
      // 如果价格部分不是数字，可能是格式不正确
      if (isNaN(parseFloat(price))) return line;
      
      // 返回标准格式：SKU名称 价格
      return skuName + ' ' + price;
    });
    
    // 4. 合并为文本，每行一个SKU
    return lines.join('\n');
  }

  console.log('弹出窗口已加载');

  fillPricesButton.addEventListener('click', function() {
    console.log('填充按钮被点击');
    const data = skuPriceDataTextarea.value;
    if (!data.trim()) {
      statusDiv.textContent = '请输入SKU价格数据';
      return;
    }

    // 解析输入的SKU价格数据
    const skuPriceMap = {};
    const lines = data.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      // 查找最后一个空格，前面是SKU名称，后面是价格
      const lastSpaceIndex = trimmedLine.lastIndexOf(' ');
      if (lastSpaceIndex === -1) continue;
      
      const skuName = trimmedLine.substring(0, lastSpaceIndex).trim();
      const price = trimmedLine.substring(lastSpaceIndex + 1).trim();
      
      if (skuName && !isNaN(parseFloat(price))) {
        skuPriceMap[skuName] = price;
      }
    }
    
    if (Object.keys(skuPriceMap).length === 0) {
      statusDiv.textContent = '未解析到有效的SKU价格数据';
      return;
    }
    
    // 执行填充脚本
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (!tabs || tabs.length === 0) {
        console.error('未找到活动标签页');
        statusDiv.textContent = '未找到活动标签页';
        return;
      }
      
      console.log('发送消息到标签页:', tabs[0].id);
      
      // 在页面中执行的填充函数，添加延时
      function fillPricesWithDelay() {
        // 这个函数将在页面中执行
        const skuPriceMap = arguments[0];
        
        // 检查是否存在SKU促销价设置弹窗
        const dialog = document.querySelector('.sku-setting-dialog');
        if (!dialog) {
          return { success: false, message: '未找到SKU促销价设置弹窗' };
        }
      
        // 获取所有SKU行
        const rows = document.querySelectorAll('.next-table-body .next-table-row');
        if (!rows || rows.length === 0) {
          return { success: false, message: '未找到SKU行' };
        }
      
        // 按照键的长度对SKU价格映射进行排序，确保更具体的SKU名称优先匹配
        const sortedSkuEntries = Object.entries(skuPriceMap).sort((a, b) => b[0].length - a[0].length);
        
        // 创建一个进度提示元素
        const progressMessage = document.createElement('div');
        progressMessage.style.position = 'fixed';
        progressMessage.style.top = '50%';
        progressMessage.style.left = '50%';
        progressMessage.style.transform = 'translate(-50%, -50%)';
        progressMessage.style.padding = '10px 20px';
        progressMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        progressMessage.style.color = 'white';
        progressMessage.style.borderRadius = '4px';
        progressMessage.style.fontSize = '16px';
        progressMessage.style.zIndex = '9999';
        document.body.appendChild(progressMessage);
        
        // 将行转换为数组
        const rowsArray = Array.from(rows);
        let filledCount = 0;
        let currentIndex = 0;
        
        // 返回一个Promise，在所有价格填充完成后解析
        return new Promise((resolve) => {
          // 逐个填充价格的函数
          function fillNextPrice() {
            if (currentIndex >= rowsArray.length) {
              // 所有价格已填充完成
              progressMessage.textContent = `填写完成！共填充 ${filledCount} 个SKU价格`;
              
              // 3秒后移除提示
              setTimeout(() => {
                document.body.removeChild(progressMessage);
                resolve({ success: true, count: filledCount });
              }, 3000);
              
              return;
            }
            
            const row = rowsArray[currentIndex];
            currentIndex++;
            
            // 更新进度提示
            progressMessage.textContent = `正在填充价格... (${currentIndex}/${rowsArray.length})`;
            
            // 获取SKU名称
            const skuNameCell = row.querySelector('[data-next-table-col="1"] .next-table-cell-wrapper');
            if (!skuNameCell) {
              // 继续下一个
              setTimeout(fillNextPrice, 100);
              return;
            }
            
            const fullSkuName = skuNameCell.textContent.trim();
            
            // 查找匹配的SKU价格（使用精确匹配）
            let matched = false;
            for (const [key, price] of sortedSkuEntries) {
              if (fullSkuName === key) {
                // 找到价格输入框
                const priceInput = row.querySelector('[data-next-table-col="3"] input');
                if (priceInput) {
                  // 设置价格
                  priceInput.value = price;
                  // 触发input事件，确保价格更新
                  priceInput.dispatchEvent(new Event('input', { bubbles: true }));
                  // 触发change事件，确保价格被系统接受
                  priceInput.dispatchEvent(new Event('change', { bubbles: true }));
                  filledCount++;
                  matched = true;
                }
                break;
              }
            }
            
            // 随机延时200-500ms后填充下一个价格，模拟人工操作
            const delay = Math.floor(Math.random() * 300) + 200;
            setTimeout(fillNextPrice, delay);
          }
          
          // 开始填充第一个价格
          fillNextPrice();
        });
      }
      
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: fillPricesWithDelay,
        args: [skuPriceMap]
      }).then(results => {
        if (results && results.length > 0) {
          const result = results[0].result;
          if (result && result.success) {
            statusDiv.textContent = '填充完成！共填充 ' + result.count + ' 个SKU价格';
          } else {
            statusDiv.textContent = result ? result.message : '填充失败，请确保已打开SKU促销价设置弹窗';
          }
        } else {
          statusDiv.textContent = '执行脚本失败';
        }
      }).catch(error => {
        console.error('执行脚本错误:', error);
        statusDiv.textContent = '执行脚本错误: ' + (error.message || '未知错误');
      });
    });
  });
  
  // 添加格式化按钮
  const formatButton = document.createElement('button');
  formatButton.textContent = '格式化';
  formatButton.style.marginRight = '10px';
  formatButton.style.backgroundColor = '#34a853';
  
  // 将格式化按钮插入到填充价格按钮前面
  fillPricesButton.parentNode.insertBefore(formatButton, fillPricesButton);
  
  // 添加格式化按钮点击事件
  formatButton.addEventListener('click', function() {
    const text = skuPriceDataTextarea.value;
    if (!text.trim()) {
      statusDiv.textContent = '没有内容可格式化';
      return;
    }
    
    // 格式化文本
    skuPriceDataTextarea.value = optimizePastedText(text);
    statusDiv.textContent = '内容已格式化';
    setTimeout(() => {
      if (statusDiv.textContent === '内容已格式化') {
        statusDiv.textContent = '';
      }
    }, 2000);
  });

  // 添加导入XLS按钮事件
  importXLSButton.addEventListener('click', function() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xls,.xlsx';
    
    input.onchange = function(e) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onload = function(e) {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, {type: 'array'});
        
        // 获取第一个工作表
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1, defval: ''});
        
        // 解析商品数据
        parsedProducts = parseProductData(jsonData);
        
        // 显示解析结果
        displayParseResult(parsedProducts);
      };
      
      reader.readAsArrayBuffer(file);
    };
    
    input.click();
  });

  // 解析商品数据
  function parseProductData(jsonData) {
    const products = new Map(); // 使用Map来存储商品，防止重复
    let currentProduct = null;
    let headers = jsonData[0];

    // 调试输出
    console.log('开始解析数据:', jsonData);

    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      
      // 跳过空行
      if (!row || row.every(cell => !cell)) continue;
      
      // 检查是否是尺码行
      if (row.some(cell => cell && cell.toString().includes('尺码：'))) {
        continue; // 只跳过尺码行，不触发商品添加
      }
      
      // 检查是否是新商品（通过检查第一列是否为商品ID）
      if (row[0] && /^\d+$/.test(row[0].toString())) {
        const productId = row[0].toString();
        if (!products.has(productId)) {
          products.set(productId, {
            id: productId,
            skus: []
          });
        }
        currentProduct = products.get(productId);
        
        // 检查同一行是否有SKU信息
        if (row[1]) {
          const skuName = row[1].toString().trim();
          if (skuName) {
            // 查找该行中的价格
            let price = null;
            for (let j = 2; j < row.length; j++) {
              if (row[j] && !isNaN(parseFloat(row[j]))) {
                price = parseFloat(row[j]);
                break;
              }
            }
            
            if (price !== null) {
              // 检查是否已存在相同的SKU
              const existingSku = currentProduct.skus.find(sku => sku.name === skuName);
              if (!existingSku) {
                currentProduct.skus.push({
                  name: skuName,
                  price: price
                });
              }
            }
          }
        }
        continue;
      }
      
      // 处理SKU行
      if (currentProduct && row[1]) {
        const skuName = row[1].toString().trim();
        if (!skuName) continue;
        
        // 查找该行中的价格
        let price = null;
        for (let j = 2; j < row.length; j++) {
          if (row[j] && !isNaN(parseFloat(row[j]))) {
            price = parseFloat(row[j]);
            break;
          }
        }
        
        if (price !== null) {
          // 检查是否已存在相同的SKU
          const existingSku = currentProduct.skus.find(sku => sku.name === skuName);
          if (!existingSku) {
            currentProduct.skus.push({
              name: skuName,
              price: price
            });
          }
        }
      }
    }
    
    // 调试输出
    console.log('解析结果:', Array.from(products.values()));
    
    // 转换Map为数组
    return Array.from(products.values());
  }

  // 显示解析结果
  function displayParseResult(products) {
    let output = `已找到 ${products.length} 个商品\n\n`;
    
    products.forEach(product => {
      output += `${product.id}\n`;
      product.skus.forEach(sku => {
        output += `${sku.name} ${sku.price.toFixed(2)}\n`;
      });
      output += '\n';
    });
    
    logContent.textContent = output;
    
    // 同时更新文本框中的数据，以便可以直接使用填充功能
    let skuPriceText = '';
    products.forEach(product => {
      product.skus.forEach(sku => {
        skuPriceText += `${sku.name} ${sku.price.toFixed(2)}\n`;
      });
    });
    skuPriceDataTextarea.value = skuPriceText.trim();
  }

  // 开始执行按钮事件
  executeTaskButton.addEventListener('click', function() {
    if (parsedProducts.length === 0) {
      logContent.textContent += '\n请先导入XLS文件！';
      return;
    }

    // 执行自动化处理
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (!tabs || tabs.length === 0) {
        logContent.textContent += '\n未找到活动标签页';
        return;
      }

      // 在页面中执行的自动化处理函数
      function autoProcessProducts() {
        const products = arguments[0];
        let currentIndex = 0;
        
        // 创建进度提示元素
        const progressMessage = document.createElement('div');
        progressMessage.style.position = 'fixed';
        progressMessage.style.top = '50%';
        progressMessage.style.left = '50%';
        progressMessage.style.transform = 'translate(-50%, -50%)';
        progressMessage.style.padding = '10px 20px';
        progressMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        progressMessage.style.color = 'white';
        progressMessage.style.borderRadius = '4px';
        progressMessage.style.fontSize = '16px';
        progressMessage.style.zIndex = '9999';
        document.body.appendChild(progressMessage);

        // 处理单个商品
        async function processProduct() {
          if (currentIndex >= products.length) {
            progressMessage.textContent = '所有商品处理完成！';
            setTimeout(() => {
              document.body.removeChild(progressMessage);
            }, 3000);
            return;
          }

          const product = products[currentIndex];
          progressMessage.textContent = `正在处理商品 ${currentIndex + 1}/${products.length}：${product.id}`;

          // 查找商品元素
          const productElement = Array.from(document.querySelectorAll('.item-promo')).find(el => {
            const idText = el.querySelector('.subtitle')?.textContent;
            return idText && idText.includes(product.id);
          });

          if (!productElement) {
            console.error('未找到商品:', product.id);
            currentIndex++;
            setTimeout(processProduct, 1000);
            return;
          }

          // 点击价格设置按钮
          const priceSettingBtn = productElement.querySelector('.link-text');
          if (!priceSettingBtn) {
            console.error('未找到价格设置按钮');
            currentIndex++;
            setTimeout(processProduct, 1000);
            return;
          }

          // 点击价格设置按钮
          priceSettingBtn.click();

          // 等待弹窗出现
          setTimeout(async () => {
            // 填充SKU价格
            const dialog = document.querySelector('.sku-setting-dialog');
            if (!dialog) {
              console.error('未找到SKU设置弹窗');
              currentIndex++;
              setTimeout(processProduct, 1000);
              return;
            }

            // 获取所有SKU行
            const rows = dialog.querySelectorAll('.next-table-body .next-table-row');
            if (!rows || rows.length === 0) {
              console.error('未找到SKU行');
              currentIndex++;
              setTimeout(processProduct, 1000);
              return;
            }

            // 填充价格（使用精确匹配）
            for (const row of rows) {
              const skuNameCell = row.querySelector('[data-next-table-col="1"] .next-table-cell-wrapper');
              if (!skuNameCell) continue;

              const skuName = skuNameCell.textContent.trim();
              const matchingSku = product.skus.find(sku => skuName === sku.name);

              if (matchingSku) {
                const priceInput = row.querySelector('[data-next-table-col="3"] input');
                if (priceInput) {
                  priceInput.value = matchingSku.price;
                  priceInput.dispatchEvent(new Event('input', { bubbles: true }));
                  priceInput.dispatchEvent(new Event('change', { bubbles: true }));
                  // 添加随机延时，模拟人工操作
                  await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 200));
                }
              }
            }

            // 等待6秒后点击确认按钮
            setTimeout(() => {
              const confirmBtn = dialog.querySelector('.next-btn-primary');
              if (confirmBtn) {
                confirmBtn.click();
                // 等待1秒后处理下一个商品
                setTimeout(() => {
                  currentIndex++;
                  processProduct();
                }, 1000);
              } else {
                console.error('未找到确认按钮');
                currentIndex++;
                processProduct();
              }
            }, 6000);
          }, 1000);
        }

        // 开始处理第一个商品
        processProduct();
      }

      // 执行自动化处理脚本
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: autoProcessProducts,
        args: [parsedProducts]
      }).then(results => {
        if (!results || results.length === 0) {
          logContent.textContent += '\n执行脚本失败';
        }
      }).catch(error => {
        console.error('执行脚本错误:', error);
        logContent.textContent += '\n执行脚本错误: ' + (error.message || '未知错误');
      });
    });
  });
}); 