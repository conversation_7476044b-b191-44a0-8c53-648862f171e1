// 后台脚本
console.log('单品宝填充价格扩展后台脚本已加载');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  console.log('background.js收到消息:', request);
  
  if (request.action === 'fillPrices') {
    // 转发消息到content script
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (!tabs || tabs.length === 0) {
        console.error('未找到活动标签页');
        sendResponse({ success: false, message: '未找到活动标签页' });
        return;
      }
      
      chrome.tabs.sendMessage(tabs[0].id, request, function(response) {
        console.log('从content script收到响应:', response);
        sendResponse(response);
      });
    });
    
    // 返回true表示将异步发送响应
    return true;
  }
}); 