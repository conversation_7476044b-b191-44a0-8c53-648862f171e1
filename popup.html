<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    textarea {
      width: 100%;
      height: 150px;
      margin-bottom: 10px;
    }
    button {
      padding: 8px 12px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 5px;
    }
    button:hover {
      background-color: #3367d6;
    }
    button#formatBtn {
      background-color: #34a853;
    }
    button#formatBtn:hover {
      background-color: #2d9249;
    }
    #status {
      margin-top: 10px;
      color: #333;
    }
    .tip {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    .button-container {
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .log-container {
      margin-top: 10px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;
    }
    #logContent {
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <h2>单品宝填充价格</h2>
  <p>请输入SKU名称和价格，每行一个，格式为：SKU名称 价格</p>
  <textarea id="skuPriceData" rows="10" placeholder="请粘贴SKU价格数据，每行一个，格式为：SKU名称 价格"></textarea>
  <div class="button-container">
    <button id="formatButton">格式化</button>
    <button id="fillPrices">填充价格</button>
  </div>
  <div class="button-container">
    <button id="importXLS">导入XLS文件</button>
    <button id="executeTask">开始执行</button>
  </div>
  <div id="logContainer" class="log-container">
    <pre id="logContent"></pre>
  </div>
  <div id="status"></div>
  <script src="xlsx.full.min.js"></script>
  <script src="popup.js"></script>
</body>
</html> 